import { employeeActionLogs, employees } from '@/services';
import { UserOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, Space, Tag, Typography, message } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';

const { Text } = Typography;

const ActionLogList: React.FC = () => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.EmployeeActionLog>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
      render: (_, record) => <Tag color="blue">#{record.id}</Tag>,
    },
    {
      title: '订单信息',
      dataIndex: 'orderId',
      key: 'orderId',
      width: 150,
      hideInSearch: true,
      render: (_, record) => (
        <div>
          <Text strong>{record.orderSn}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            ¥{record.totalFee}
          </Text>
        </div>
      ),
    },
    {
      title: '订单编号',
      dataIndex: 'orderId',
      key: 'orderIdSearch',
      hideInTable: true,
      valueType: 'digit',
    },
    {
      title: '员工信息',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 150,
      valueType: 'select',
      request: async () => {
        const { errCode, data } = await employees.index({});
        if (errCode || !data?.list) return [];
        return data.list.map((emp) => ({
          label: `${emp.name} (${emp.phone})`,
          value: emp.id,
        }));
      },
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.employee?.avatar}
            icon={<UserOutlined />}
            size="small"
          />
          <div>
            <Text strong>{record.employee?.name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.employee?.phone}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '客户信息',
      dataIndex: 'customerId',
      key: 'customerId',
      width: 150,
      hideInSearch: true,
      render: (_, record) => (
        <div>
          <Text strong>{record.customer?.nickname}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.customer?.phone}
          </Text>
        </div>
      ),
    },
    {
      title: '动作类型',
      dataIndex: 'changeType',
      key: 'changeType',
      width: 120,
      valueType: 'select',
      valueEnum: {
        接单: { text: '接单', status: 'Processing' },
        派单: { text: '派单', status: 'Default' },
        转单: { text: '转单', status: 'Warning' },
        修改服务时间: { text: '修改服务时间', status: 'Default' },
        出发: { text: '出发', status: 'Processing' },
        开始服务: { text: '开始服务', status: 'Success' },
        完成订单: { text: '完成订单', status: 'Success' },
        取消订单: { text: '取消订单', status: 'Error' },
        申请退款: { text: '申请退款', status: 'Warning' },
        退款: { text: '退款', status: 'Error' },
      },
      render: (_, record) => <Tag color="blue">{record.changeTypeLabel}</Tag>,
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      width: 100,
      hideInSearch: true,
      render: (status) => <Tag color="green">{status}</Tag>,
    },
    {
      title: '服务时间',
      dataIndex: 'serviceTime',
      key: 'serviceTime',
      width: 150,
      hideInSearch: true,
      render: (time) => {
        if (!time) return '-';
        return <Text>{dayjs(time as string).format('YYYY-MM-DD HH:mm')}</Text>;
      },
    },
    {
      title: '动作时间',
      dataIndex: 'actionTime',
      key: 'actionTime',
      width: 150,
      valueType: 'dateRange',
      render: (_, record) => (
        <Text>{dayjs(record.actionTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      hideInSearch: true,
      render: (description) =>
        description ? (
          <Text ellipsis={{ tooltip: description }} style={{ maxWidth: 180 }}>
            {description}
          </Text>
        ) : (
          <Text type="secondary">无描述</Text>
        ),
    },
  ];

  return (
    <ProTable<API.EmployeeActionLog>
      actionRef={actionRef}
      rowKey="id"
      columns={columns}
      request={async (params) => {
        const { errCode, msg, data } = await employeeActionLogs.index({
          ...params,
          startDate: params.actionTime?.[0],
          endDate: params.actionTime?.[1],
          orderId: params.orderIdSearch,
        });
        if (errCode) {
          message.error(msg || '列表查询失败');
          return {
            data: [],
            total: 0,
          };
        }
        return {
          data: data?.list || [],
          total: data?.total || 0,
        };
      }}
      scroll={{ x: '100%' }}
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
      }}
      pagination={{
        defaultPageSize: 20,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
    />
  );
};

export default ActionLogList;
