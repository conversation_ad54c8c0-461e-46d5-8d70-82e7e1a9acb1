import { employeeActionLogs, employees } from '@/services';
import { UserOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormDigit,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Card, Empty, Tag, Timeline, Typography, message } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';

const { Text } = Typography;

const ActionTimeline: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [timeline, setTimeline] = useState<API.EmployeeActionTimeline[]>([]);
  const [filters, setFilters] = useState<{
    employeeId?: number;
    startDate?: string;
    endDate?: string;
    orderId?: number;
  }>({});



  const handleSearch = async (values: any) => {
    if (!values.employeeId) {
      message.warning('请先选择员工');
      return;
    }

    const newFilters: any = {
      employeeId: values.employeeId,
    };

    // 添加可选参数
    if (values.orderId) {
      newFilters.orderId = values.orderId;
    }

    if (values.dateRange && values.dateRange.length === 2) {
      newFilters.startDate = values.dateRange[0]?.format('YYYY-MM-DD');
      newFilters.endDate = values.dateRange[1]?.format('YYYY-MM-DD');
    }

    setFilters(newFilters);

    // 调用获取时间线API
    setLoading(true);
    try {
      console.log('查询参数:', newFilters); // 添加调试日志
      const { errCode, msg, data } = await employeeActionLogs.timeline(newFilters);

      if (errCode) {
        message.error(msg || '获取时间线失败');
        setTimeline([]);
      } else {
        console.log('获取到的数据:', data); // 添加调试日志
        setTimeline(data || []);
        if (!data || data.length === 0) {
          message.info('该员工在指定条件下暂无动作记录');
        }
      }
    } catch (error) {
      console.error('获取时间线失败:', error);
      message.error('获取时间线失败，请重试');
      setTimeline([]);
    } finally {
      setLoading(false);
    }
  };

  const getActionColor = (changeType: string) => {
    const colorMap: Record<string, string> = {
      接单: '#1890ff',
      派单: '#722ed1',
      转单: '#fa8c16',
      修改服务时间: '#13c2c2',
      出发: '#52c41a',
      开始服务: '#1890ff',
      完成订单: '#52c41a',
      取消订单: '#ff4d4f',
      申请退款: '#fa8c16',
      退款: '#ff4d4f',
    };
    return colorMap[changeType] || '#1890ff';
  };

  const timelineItems = timeline.map((item) => ({
    color: getActionColor(item.changeType),
    children: (
      <div>
        <div style={{ marginBottom: 8 }}>
          <Tag color={getActionColor(item.changeType)}>
            {item.changeTypeLabel}
          </Tag>
          <Text strong style={{ marginLeft: 8 }}>
            订单 {item.orderSn}
          </Text>
        </div>
        <div style={{ marginBottom: 4 }}>
          <Text type="secondary">
            {dayjs(item.actionTime).format('YYYY-MM-DD HH:mm:ss')}
          </Text>
        </div>
        {item.description && (
          <div>
            <Text>{item.description}</Text>
          </div>
        )}
      </div>
    ),
  }));

  return (
    <div>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <ProForm
          layout="horizontal"
          onFinish={handleSearch}
          submitter={{
            searchConfig: {
              submitText: '查询时间线',
            },
            resetButtonProps: {
              style: { display: 'none' },
            },
          }}
          grid
        >
          <ProFormSelect
            name="employeeId"
            label="选择员工"
            placeholder="请选择员工（必填）"
            rules={[{ required: true, message: '请选择员工' }]}
            colProps={{ span: 6 }}
            request={async () => {
              const { errCode, data } = await employees.index({});
              if (errCode || !data?.list) return [];
              return data.list.map((emp) => ({
                label: `${emp.name} (${emp.phone})`,
                value: emp.id,
              }));
            }}
          />
          <ProFormDateRangePicker
            name="dateRange"
            label="时间范围"
            placeholder={['开始日期', '结束日期']}
            colProps={{ span: 6 }}
          />
          <ProFormDigit
            name="orderId"
            label="订单ID"
            placeholder="可选，筛选特定订单"
            colProps={{ span: 6 }}
            fieldProps={{
              precision: 0,
            }}
          />
        </ProForm>
      </Card>

      {/* 时间线内容 */}
      <Card
        title={
          <div>
            <UserOutlined style={{ marginRight: 8 }} />
            员工动作时间线
            {timeline.length > 0 && (
              <Text type="secondary" style={{ marginLeft: 16, fontSize: 14 }}>
                共 {timeline.length} 条记录
              </Text>
            )}
          </div>
        }
        loading={loading}
      >
        {timeline.length > 0 ? (
          <Timeline
            mode="left"
            items={timelineItems}
            style={{ marginTop: 16 }}
          />
        ) : (
          <Empty
            description={
              filters.employeeId ? '暂无动作记录' : '请选择员工查看动作时间线'
            }
            style={{ margin: '40px 0' }}
          />
        )}
      </Card>
    </div>
  );
};

export default ActionTimeline;
