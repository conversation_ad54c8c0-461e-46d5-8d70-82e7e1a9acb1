import {
  create,
  exportCustomers,
  index,
  remove,
  update,
  updateStatus,
} from '@/services/customer';
import { DownloadOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Segmented, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const Customer: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Customer | undefined>(undefined);
  const [exporting, setExporting] = useState(false);
  const [searchParams, setSearchParams] = useState<Record<string, any>>({});

  const handleSave = async (values: API.Customer) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleUpdateStatus = async (id: number, values: number) => {
    console.log('values', values);
    const response = await updateStatus(id, { status: values });

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Customer) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  // 处理导出
  const handleExport = async () => {
    try {
      setExporting(true);

      // 使用当前搜索条件
      const exportParams: any = {};

      // 处理搜索参数
      if (searchParams.phone) exportParams.phone = searchParams.phone;
      if (searchParams.nickname) exportParams.nickname = searchParams.nickname;
      if (searchParams.memberStatus !== undefined)
        exportParams.memberStatus = searchParams.memberStatus;
      if (searchParams.status !== undefined)
        exportParams.status = searchParams.status;

      // 调用导出接口
      const { errCode, msg, data } = await exportCustomers(exportParams);

      // 检查响应状态
      if (errCode !== 0) {
        message.error(msg || '导出失败');
        return;
      }

      // 从响应中获取文件数据
      const fileBuffer = data?.data;
      if (!fileBuffer) {
        message.error('导出数据为空');
        return;
      }

      // 将 ArrayBuffer 转换为 Uint8Array，然后创建 Blob
      const uint8Array = new Uint8Array(fileBuffer);
      const blob = new Blob([uint8Array], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      // 使用文件流方式创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const now = dayjs();
      const fileName = `customers_${now.format('YYYYMMDD_HHmmss')}.xlsx`;
      link.download = fileName;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    } finally {
      setExporting(false);
    }
  };

  const columns: ProColumns<API.Customer, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 100,
      fixed: 'left',
    },
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 50,
      align: 'center',
      valueType: 'avatar',
      hideInSearch: true,
    },
    {
      title: '性别',
      dataIndex: 'gender',
      key: 'gender',
      width: 50,
      align: 'center',
      valueEnum: {
        2: '保密',
        1: '男',
        0: '女',
      },
      hideInSearch: true,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '会员状态',
      dataIndex: 'memberStatus',
      key: 'memberStatus',
      width: 100,
      align: 'center',
      valueEnum: {
        1: '权益会员',
        0: '普通会员',
      },
      filters: true,
    },
    {
      title: '积分值',
      dataIndex: 'points',
      key: 'points',
      width: 80,
      align: 'center',
      hideInSearch: true,
      valueType: 'digit',
    },
    {
      title: '推广员工',
      dataIndex: ['promotionEmployee', 'name'],
      key: 'promotionEmployeeId',
      width: 100,
      hideInSearch: true,
      render: (_, record) => {
        return record.promotionEmployee?.name || '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
      valueEnum: {
        1: '启用',
        0: '禁用',
      },
      filters: true,
      hideInSearch: true,
      render: (_, entity) => {
        return (
          <Segmented
            size="small"
            value={entity.status}
            options={[
              {
                label: '启用',
                value: 1,
              },
              {
                label: '禁用',
                value: 0,
              },
            ]}
            onChange={(value) => {
              handleUpdateStatus(entity.id, value);
            }}
          />
        );
      },
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Customer>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params, sort, filter) => {
          // 保存搜索参数用于导出
          setSearchParams(params);

          const { errCode, msg, data } = await index({
            ...params,
            sort,
            filter,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        scroll={{ x: '100%' }}
        toolBarRender={() => [
          <Button
            key="export"
            icon={<DownloadOutlined />}
            onClick={handleExport}
            loading={exporting}
          >
            导出Excel
          </Button>,
          // <Button
          //   key="add"
          //   type="primary"
          //   onClick={() => {
          //     setCurrent(undefined);
          //     setModalVisible(true);
          //   }}
          // >
          //   新增
          // </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default Customer;
